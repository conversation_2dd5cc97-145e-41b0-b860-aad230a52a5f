# Tauri 子窗口使用指南

## 概述

本项目实现了类似 Electron 的子窗口功能，支持两种模式：
- **非模态窗口**：不影响主窗口操作，可以同时操作多个窗口
- **模态窗口**：阻止主窗口操作，必须先关闭子窗口才能继续操作主窗口

## 功能特性

### ✅ 已实现功能

1. **子窗口创建**
   - 支持自定义窗口大小、标题、URL
   - 支持模态和非模态两种模式
   - 自动检测窗口是否已存在，避免重复创建

2. **模态窗口管理**
   - 自动禁用/启用父窗口
   - 视觉反馈（遮罩层、提示信息）
   - 窗口关闭时自动恢复父窗口状态

3. **窗口控制**
   - 程序化关闭子窗口
   - 窗口状态监听
   - 批量关闭功能

4. **前端集成**
   - Vue 组合式函数封装
   - 自动事件监听和清理
   - 响应式状态管理

## API 参考

### Rust 后端命令

#### `create_child_window`
创建子窗口

```rust
#[tauri::command]
async fn create_child_window(
    app: AppHandle,
    window_id: String,        // 窗口唯一标识
    title: String,            // 窗口标题
    url: String,              // 窗口URL（支持相对路径和绝对URL）
    modal: Option<bool>,      // 是否为模态窗口（默认false）
    width: Option<f64>,       // 窗口宽度（默认800）
    height: Option<f64>,      // 窗口高度（默认600）
    parent_window: Option<String>, // 父窗口标识（默认"main"）
) -> Result<(), String>
```

#### `close_child_window`
关闭子窗口

```rust
#[tauri::command]
async fn close_child_window(
    app: AppHandle,
    window_id: String,        // 要关闭的窗口ID
    parent_window: Option<String>, // 父窗口标识
) -> Result<(), String>
```

### 前端 JavaScript API

#### 基本使用

```javascript
import { invoke } from '@tauri-apps/api/core'

// 创建非模态窗口
await invoke('create_child_window', {
  windowId: 'my-window',
  title: '我的窗口',
  url: '/my-page',
  modal: false,
  width: 800,
  height: 600,
  parentWindow: 'main'
})

// 创建模态窗口
await invoke('create_child_window', {
  windowId: 'modal-window',
  title: '模态窗口',
  url: '/modal-page',
  modal: true,
  width: 600,
  height: 400,
  parentWindow: 'main'
})

// 关闭窗口
await invoke('close_child_window', {
  windowId: 'my-window',
  parentWindow: 'main'
})
```

#### 使用组合式函数

```javascript
import { useWindowModal } from '@/composables/useWindowModal.js'

export default {
  setup() {
    const { isWindowDisabled, setWindowDisabled } = useWindowModal()
    
    return {
      isWindowDisabled,
      setWindowDisabled
    }
  }
}
```

## 使用示例

### 1. 表单子窗口

```javascript
// 打开复杂表单窗口
const openFormWindow = async (modal = false) => {
  await invoke('create_child_window', {
    windowId: modal ? 'modal-form' : 'form-window',
    title: modal ? '模态表单' : '表单窗口',
    url: '/form-page',
    modal,
    width: 900,
    height: 700,
    parentWindow: 'main'
  })
}
```

### 2. 设置窗口

```javascript
// 打开设置窗口
const openSettings = async () => {
  await invoke('create_child_window', {
    windowId: 'settings',
    title: '系统设置',
    url: '/settings-page',
    modal: true,  // 设置通常使用模态窗口
    width: 800,
    height: 600,
    parentWindow: 'main'
  })
}
```

### 3. 数据查看器

```javascript
// 打开数据查看器
const openDataViewer = async () => {
  await invoke('create_child_window', {
    windowId: 'data-viewer',
    title: '数据查看器',
    url: '/data-page',
    modal: false,  // 数据查看器通常不需要模态
    width: 1200,
    height: 800,
    parentWindow: 'main'
  })
}
```

## 最佳实践

### 1. 窗口ID 命名规范
- 使用描述性名称：`form-window`, `settings-modal`, `data-viewer`
- 模态窗口可以添加 `modal-` 前缀
- 避免使用特殊字符，使用连字符分隔

### 2. 模态窗口使用场景
**适合使用模态窗口：**
- 重要设置和配置
- 确认对话框
- 关键数据编辑
- 需要用户专注的操作

**适合使用非模态窗口：**
- 数据查看和浏览
- 辅助工具窗口
- 多任务操作场景
- 参考信息显示

### 3. 窗口大小建议
- **小型对话框**：400x300 - 600x400
- **表单窗口**：800x600 - 1000x700
- **数据查看器**：1200x800 - 1400x900
- **设置窗口**：800x600

### 4. 错误处理

```javascript
const openWindow = async () => {
  try {
    await invoke('create_child_window', {
      // ... 参数
    })
    message.success('窗口已打开')
  } catch (error) {
    console.error('打开窗口失败:', error)
    message.error(`打开窗口失败: ${error}`)
  }
}
```

## 注意事项

1. **窗口ID 唯一性**：确保每个窗口使用唯一的 ID
2. **内存管理**：及时关闭不需要的窗口
3. **用户体验**：避免同时打开过多窗口
4. **模态窗口**：确保用户能够明确知道如何关闭模态窗口
5. **响应式设计**：子窗口页面应该适配不同的窗口大小

## 故障排除

### 常见问题

1. **窗口无法打开**
   - 检查 URL 路径是否正确
   - 确认路由配置是否存在
   - 查看控制台错误信息

2. **模态窗口无法禁用主窗口**
   - 确认 `useWindowModal` 组合式函数已正确导入
   - 检查事件监听是否正常工作

3. **窗口重复创建**
   - 检查窗口 ID 是否唯一
   - 确认窗口关闭逻辑是否正确

### 调试技巧

1. 开启开发者工具查看子窗口
2. 使用 `console.log` 跟踪窗口状态
3. 检查 Tauri 后端日志输出

## 扩展功能

可以考虑添加的功能：
- 窗口位置记忆
- 窗口大小自适应
- 窗口间通信
- 窗口组管理
- 自定义窗口样式
