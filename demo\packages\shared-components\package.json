{"name": "@cost-app/shared-components", "version": "1.0.0", "type": "module", "main": "dist/index.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.5.13", "ant-design-vue": "^4.2.6", "tabulator-tables": "^6.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vue-tsc": "^2.2.8", "typescript": "~5.8.0"}, "peerDependencies": {"vue": "^3.5.13", "ant-design-vue": "^4.2.6"}}