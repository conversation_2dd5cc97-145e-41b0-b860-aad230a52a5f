{"name": "@cost-app/shared-components", "version": "1.0.0", "type": "module", "main": "dist/index.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite build --watch --mode development", "build": "vite build --mode production", "build:watch": "vite build --watch", "serve": "vite", "preview": "vite preview"}, "dependencies": {"ant-design-vue": "^4.2.6", "dayjs": "^1.11.13", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vue-tsc": "^2.2.8"}, "peerDependencies": {"ant-design-vue": "^4.2.6", "vue": "^3.5.13"}}