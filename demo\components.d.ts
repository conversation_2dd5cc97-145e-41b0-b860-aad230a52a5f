/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACol: typeof import('ant-design-vue/es')['Col']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASpace: typeof import('ant-design-vue/es')['Space']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ChildWindowDemo: typeof import('./src/components/ChildWindowDemo.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
