# 概算表单窗口使用指南

## 概述

本指南介绍如何在概算模块中使用表单窗口功能，包括：
- 基于 Ant Design Table 的新表格组件
- 公用的表单组件
- 子窗口中的表单操作
- 数据的获取和同步

## 功能特性

### ✅ 已实现功能

1. **新的 CostTable 组件**
   - 基于 Ant Design Table，替换了 Tabulator
   - 支持排序、筛选、分页
   - 内置操作按钮（新增、删除、导出、表单编辑）
   - 响应式设计，支持固定列

2. **公用 CostForm 组件**
   - 支持创建、编辑、查看三种模式
   - 完整的表单验证
   - 费用明细动态表格
   - 自动计算总金额

3. **子窗口集成**
   - 在概算中打开表单窗口
   - 支持模态和非模态模式
   - 数据传递和同步
   - 窗口状态管理

## 组件使用

### 1. CostTable 组件

```vue
<template>
  <CostTable
    :data="tableData"
    :columns="tableColumns"
    table-type="estimate"
    :editable="true"
    @open-form="handleOpenForm"
    @edit-row="handleEditRow"
    @delete-row="handleDeleteRow"
    @row-select="handleRowSelect"
  />
</template>

<script setup>
import { CostTable } from '@cost-app/shared-components'

// 表格列配置
const tableColumns = [
  { 
    title: '项目名称', 
    dataIndex: 'name', 
    key: 'name', 
    width: 200,
    sorter: true
  },
  { 
    title: '金额', 
    dataIndex: 'amount', 
    key: 'amount', 
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 事件处理
const handleOpenForm = ({ type, data }) => {
  // type: 'create' | 'edit' | 'view'
  // data: 选中的数据或 null
}

const handleEditRow = (record) => {
  // 编辑单行数据
}

const handleDeleteRow = (record) => {
  // 删除单行数据
}

const handleRowSelect = (rows) => {
  // 处理行选择
}
</script>
```

### 2. CostForm 组件

```vue
<template>
  <CostForm
    :mode="formMode"
    :initial-data="initialData"
    :form-type="formType"
    @submit="handleFormSubmit"
    @cancel="handleFormCancel"
    @save-draft="handleSaveDraft"
  />
</template>

<script setup>
import { CostForm } from '@cost-app/shared-components'

const formMode = ref('create') // 'create' | 'edit' | 'view'
const formType = ref('estimate') // 'estimate' | 'budget' | 'settlement'
const initialData = ref({})

const handleFormSubmit = (formData) => {
  // 处理表单提交
  console.log('表单数据:', formData)
}

const handleFormCancel = () => {
  // 处理取消操作
}

const handleSaveDraft = (formData) => {
  // 处理保存草稿
}
</script>
```

## 在概算中的使用

### 1. 表格配置

在概算模块中，表格列配置需要使用正确的格式：

```javascript
const costTableColumns = [
  { 
    title: '项目名称', 
    dataIndex: 'name', 
    key: 'name', 
    width: 200,
    sorter: true
  },
  { 
    title: '项目类型', 
    dataIndex: 'type', 
    key: 'type', 
    width: 120
  },
  { 
    title: '概算金额', 
    dataIndex: 'amount', 
    key: 'amount', 
    width: 150,
    sorter: true
  },
  { 
    title: '状态', 
    dataIndex: 'status', 
    key: 'status', 
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]
```

### 2. 数据结构

确保数据包含必要的字段：

```javascript
const estimateData = ref([
  {
    id: 1,
    name: '办公楼建设项目',
    type: 'building',
    amount: 5000000,
    status: 'approved',
    createTime: '2024-01-15',
    creator: '张三',
    description: '项目描述',
    manager: '张三',
    startDate: '2024-02-01',
    endDate: '2024-12-31'
  }
])
```

### 3. 打开表单窗口

```javascript
const openFormWindow = async (mode = 'create', data = null) => {
  try {
    const windowId = `estimate-form-${mode}-${Date.now()}`
    const title = mode === 'create' ? '新建概算' : mode === 'edit' ? '编辑概算' : '查看概算'
    
    // 构建URL参数
    const params = new URLSearchParams({
      mode,
      formType: 'estimate'
    })
    
    if (data) {
      params.append('data', JSON.stringify(data))
    }
    
    await invoke('create_child_window', {
      windowId,
      title,
      url: `/form-page?${params.toString()}`,
      modal: mode !== 'view', // 查看模式使用非模态
      width: 1200,
      height: 800,
      parentWindow: 'main'
    })
    
    message.success(`${title}窗口已打开`)
  } catch (error) {
    console.error('打开表单窗口失败:', error)
    message.error('打开窗口失败')
  }
}
```

### 4. 事件处理

```javascript
// 表格事件处理
const handleOpenForm = ({ type, data }) => {
  openFormWindow(type, data)
}

const handleEditRow = (record) => {
  openFormWindow('edit', record)
}

const handleDeleteRow = (record) => {
  const index = estimateData.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    estimateData.value.splice(index, 1)
    message.success(`已删除 ${record.name}`)
  }
}

const handleRowSelect = (rows) => {
  if (rows.length > 0) {
    message.info(`已选中 ${rows.length} 项`)
  }
}
```

## 表单页面配置

### FormPage.vue 配置

表单页面会自动从 URL 参数中获取配置：

```javascript
// 从URL参数获取表单配置
const urlParams = new URLSearchParams(window.location.search)
const formMode = ref(urlParams.get('mode') || 'create')
const formType = ref(urlParams.get('formType') || 'estimate')

// 解析初始数据
const initialData = ref({})
try {
  const dataParam = urlParams.get('data')
  if (dataParam) {
    initialData.value = JSON.parse(dataParam)
  }
} catch (error) {
  console.error('解析初始数据失败:', error)
}
```

## 数据流程

### 1. 创建流程
1. 用户点击"新建概算"按钮
2. 调用 `openFormWindow('create', null)`
3. 打开表单窗口，模式为创建
4. 用户填写表单并提交
5. 表单数据通过 `handleFormSubmit` 处理
6. 保存成功后关闭窗口

### 2. 编辑流程
1. 用户点击表格中的"编辑"按钮
2. 调用 `openFormWindow('edit', record)`
3. 打开表单窗口，预填充数据
4. 用户修改表单并提交
5. 更新原数据并关闭窗口

### 3. 查看流程
1. 用户点击"查看详情"
2. 调用 `openFormWindow('view', record)`
3. 打开只读表单窗口
4. 用户查看后关闭窗口

## 样式和主题

### 表格样式
- 使用 Ant Design 默认主题
- 支持响应式布局
- 固定操作列
- 悬停高亮效果

### 表单样式
- 卡片式布局
- 垂直表单布局
- 响应式栅格系统
- 统一的操作按钮样式

## 故障排除

### 常见问题

1. **表格不显示数据**
   - 检查数据结构是否正确
   - 确认列配置的 `dataIndex` 和 `key` 字段
   - 查看控制台错误信息

2. **表单窗口无法打开**
   - 检查 Tauri 后端是否正常运行
   - 确认路由配置是否正确
   - 查看网络请求是否成功

3. **数据不同步**
   - 确认事件处理函数是否正确绑定
   - 检查数据更新逻辑
   - 验证响应式数据绑定

### 调试技巧

1. 使用浏览器开发者工具查看组件状态
2. 在控制台查看事件触发日志
3. 检查 Vue DevTools 中的组件数据
4. 使用 `/table-test` 路由测试基本功能

## 扩展功能

可以考虑添加的功能：
- 批量操作（批量删除、批量导出）
- 高级筛选和搜索
- 列配置保存
- 数据导入导出
- 表单模板功能
- 审批流程集成
