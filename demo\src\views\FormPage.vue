<template>
  <div class="form-page">
    <div class="form-header">
      <h2>📝 复杂表单操作</h2>
      <p>这是一个子窗口中的复杂表单示例</p>
    </div>

    <a-form
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="onFinish"
      @finishFailed="onFinishFailed"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="项目名称" name="projectName">
            <a-input v-model:value="formData.projectName" placeholder="请输入项目名称" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="项目类型" name="projectType">
            <a-select v-model:value="formData.projectType" placeholder="请选择项目类型">
              <a-select-option value="building">建筑工程</a-select-option>
              <a-select-option value="infrastructure">基础设施</a-select-option>
              <a-select-option value="renovation">装修工程</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="预算金额" name="budget">
            <a-input-number
              v-model:value="formData.budget"
              :min="0"
              :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/¥\s?|(,*)/g, '')"
              style="width: 100%"
              placeholder="请输入预算金额"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="开始日期" name="startDate">
            <a-date-picker v-model:value="formData.startDate" style="width: 100%" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="结束日期" name="endDate">
            <a-date-picker v-model:value="formData.endDate" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="项目描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          :rows="4"
          placeholder="请输入项目详细描述"
        />
      </a-form-item>

      <a-form-item label="费用明细">
        <a-table
          :columns="costColumns"
          :data-source="costItems"
          :pagination="false"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'name'">
              <a-input v-model:value="record.name" placeholder="费用项目" />
            </template>
            <template v-else-if="column.key === 'amount'">
              <a-input-number
                v-model:value="record.amount"
                :min="0"
                style="width: 100%"
                placeholder="金额"
                @change="calculateTotal"
              />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button type="link" danger @click="removeCostItem(index)">
                删除
              </a-button>
            </template>
          </template>
        </a-table>
        
        <div style="margin-top: 8px;">
          <a-button @click="addCostItem" type="dashed" block>
            <template #icon><PlusOutlined /></template>
            添加费用项目
          </a-button>
        </div>
        
        <div class="total-amount">
          <strong>总计：¥{{ totalAmount.toLocaleString() }}</strong>
        </div>
      </a-form-item>

      <a-form-item>
        <div class="form-actions">
          <a-button @click="closeWindow">取消</a-button>
          <a-button type="primary" html-type="submit" :loading="saving">
            保存
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'
import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow'
import { PlusOutlined } from '@ant-design/icons-vue'

const saving = ref(false)

const formData = reactive({
  projectName: '',
  projectType: '',
  budget: null,
  startDate: null,
  endDate: null,
  description: ''
})

const rules = {
  projectName: [{ required: true, message: '请输入项目名称' }],
  projectType: [{ required: true, message: '请选择项目类型' }],
  budget: [{ required: true, message: '请输入预算金额' }]
}

const costItems = ref([
  { name: '材料费', amount: 0 },
  { name: '人工费', amount: 0 },
  { name: '机械费', amount: 0 }
])

const costColumns = [
  { title: '费用项目', key: 'name', width: '40%' },
  { title: '金额（元）', key: 'amount', width: '40%' },
  { title: '操作', key: 'action', width: '20%' }
]

const totalAmount = computed(() => {
  return costItems.value.reduce((sum, item) => sum + (item.amount || 0), 0)
})

const addCostItem = () => {
  costItems.value.push({ name: '', amount: 0 })
}

const removeCostItem = (index) => {
  costItems.value.splice(index, 1)
  calculateTotal()
}

const calculateTotal = () => {
  // 触发计算更新
}

const onFinish = async (values) => {
  try {
    saving.value = true
    
    const formDataWithCosts = {
      ...values,
      costItems: costItems.value,
      totalAmount: totalAmount.value
    }
    
    console.log('表单数据:', formDataWithCosts)
    
    // 这里可以调用后端API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟保存
    
    message.success('表单保存成功！')
    
    // 保存成功后关闭窗口
    setTimeout(() => {
      closeWindow()
    }, 1000)
    
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const onFinishFailed = (errorInfo) => {
  console.log('表单验证失败:', errorInfo)
  message.error('请检查表单填写')
}

const closeWindow = async () => {
  try {
    const currentWindow = getCurrentWebviewWindow()
    await currentWindow.close()
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

onMounted(() => {
  // 监听窗口禁用/启用事件（用于模态窗口）
  const currentWindow = getCurrentWebviewWindow()
  
  currentWindow.listen('window-disabled', () => {
    console.log('窗口被禁用')
  })
  
  currentWindow.listen('window-enabled', () => {
    console.log('窗口被启用')
  })
})
</script>

<style scoped>
.form-page {
  padding: 24px;
  background: #fff;
  min-height: 100vh;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.form-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

.total-amount {
  margin-top: 16px;
  text-align: right;
  font-size: 16px;
  color: #1890ff;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}
</style>
